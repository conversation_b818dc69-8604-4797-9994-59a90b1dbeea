function 同步在线数据() {
    try {
        // 获取在线工作簿
        let onlineApp = GetObject("", "KET.Application");
        let onlineWorkbook = onlineApp.ActiveWorkbook;
        let onlineSheet = onlineWorkbook.ActiveSheet;
        
        // 获取本地工作簿
        let localApp = new ActiveXObject("KET.Application");
        
        // 如果本地没有打开文件，创建新的工作簿
        let localWorkbook;
        try {
            localWorkbook = localApp.ActiveWorkbook;
        } catch {
            localWorkbook = localApp.Workbooks.Add();
        }
        let localSheet = localWorkbook.ActiveSheet;

        // 获取在线表格的使用范围
        let lastRow = onlineSheet.UsedRange.Rows.Count;
        let lastCol = onlineSheet.UsedRange.Columns.Count;
        
        // 复制数据范围
        let sourceRange = onlineSheet.Range(
            onlineSheet.Cells(1, 1),
            onlineSheet.Cells(lastRow, lastCol)
        );
        
        // 复制到本地表格
        sourceRange.Copy();
        localSheet.Range("A1").PasteSpecial();
        
        // 清除剪贴板
        localApp.CutCopyMode = false;
        
        // 保存本地文件
        let today = new Date();
        let fileName = `同步数据_${today.getFullYear()}${(today.getMonth()+1).toString().padStart(2,'0')}${today.getDate().toString().padStart(2,'0')}.xlsx`;
        localWorkbook.SaveAs(fileName);
        
        // 显示成功消息
        alert(`数据已同步到本地文件：${fileName}`);
        
    } catch (error) {
        alert("同步失败：" + error.message);
    }
}

// 执行同步
同步在线数据(); 