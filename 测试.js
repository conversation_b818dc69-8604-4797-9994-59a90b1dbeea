function 处理修改签单数据(修改签单工作表) {
  const 修改签单数据 = 修改签单工作表.UsedRange.Value;
  const 修改状态列索引 = 39; // 第36列的索引为35
  let 需要修改的值数组 = []; // 定义一个数组来存储需要修改的值

  修改签单数据.forEach((item, index) => {
    if (index === 0) return; // 跳过标题行

    if (item[修改状态列索引] !== "已修改") {
      需要修改的值数组.push(item[4]); // 将第5列的值添加到数组中
    }
  });

  return 需要修改的值数组; // 返回存储需要修改的值的数组
}

function updateSheetsWithData(allSheetsData) {
  // let startTime = Date.now();

  let 新增签单Data = allSheetsData["新增签单"];
  let 缺失的数据 = [];
  let 关注的城市数据 = {};
  let 关注的城市 = ["舟山", "慈溪", "余姚", "江北", "北仑", "海曙", "下应", "建庄", "奉化", "宁海", "象山", "杭州"];

  // 预处理关注的城市数据
  关注的城市.forEach(city => {
    if (allSheetsData.hasOwnProperty(city)) {
      关注的城市数据[city] = new Set(Array.from(allSheetsData[city].values()).map(rowData => rowData.data[1]));
    }
  });

  // 处理新增签单数据
  新增签单Data.forEach((value, index) => {
    let city = value.data[21].includes("杭州") ? "杭州" : value.data[21];
    let keyValue = value.data[1];

    if (关注的城市.includes(city) && !关注的城市数据[city].has(keyValue)) {
      缺失的数据.push({ key: keyValue, row: value.row, city: value.data[21] });
    }
  });

  // 处理需要修改的值
  let 需要修改的值 = 需要修改的值数组;

  需要修改的值.forEach(val => {
    let dataRow = 新增签单Data.get(val);
    if (dataRow) {
      缺失的数据.push({ key: val, row: dataRow.row, city: dataRow.data[21] });
    } else {
      缺失的数据.push({ key: val, row: undefined, city: undefined });
    }
  });

  console.log("总结的数据:", 缺失的数据);

  // let endTime = Date.now();
  // let elapsedTime = endTime - startTime;
  // console.log(`函数执行时间：${elapsedTime}毫秒`);

  return 缺失的数据; // 返回总结的数据数组

}

// 函数从单个工作表读取数据并创建Map
function getDataFromSheet(sheet) {
  let sheetData = sheet.UsedRange.Value;
  let dataMap = new Map();

  sheetData.forEach((item, index) => {
    if (index === 0) return; // 跳过标题行
    let key = item[1]; // 假设第二个单元格是唯一标识符
    if (key) {
      dataMap.set(key, { row: index + 1, data: item });
    }
  });

  return dataMap;
}

// 函数遍历所有工作表并构建allSheetsData对象
function 建立所有数据(sheets) {
  let allSheetsData = {};

  for (let i = 1; i <= 14; i++) {
    let sheet = sheets.Item(i);
    allSheetsData[sheet.Name] = getDataFromSheet(sheet);
  }

  return allSheetsData;
}

function processAndCopyData(总结的数据) {
  xiugai(需要修改的值数组)
  总结的数据.forEach(dataItem => {
    let row = dataItem.row;
    let key = dataItem.key;
    let city = dataItem.city.includes("杭州") ? "杭州" : dataItem.city;

    // 获取源工作表
    let sourceSheet = Application.ActiveWorkbook.Sheets.Item("新增签单");
    let sourceRange = sourceSheet.Range(`A${row}:AD${row}`); // 复制第1-28列
    sourceRange.Copy();

    // 获取目标工作表
    let targetSheet = Application.ActiveWorkbook.Sheets.Item(city);
    let targetRangeRow;

    if (targetSheet) {
      // 检查目标工作表中是否已存在该数据
      let targetData = targetSheet.UsedRange.Value; // 获取目标工作表的所有数据
      let existingRow = findRowByKey(targetData, key); // 假设此函数能找到对应 key 的行号

      if (existingRow) {
        // 如果数据已存在，覆盖现有行
        targetRangeRow = existingRow;
      } else {
        // 如果数据不存在，粘贴到最后一行
        targetRangeRow = targetSheet.UsedRange.Rows.Count + 1;
      }

      let targetRange = targetSheet.Range(`A${targetRangeRow}:AB${targetRangeRow}`);
      targetRange.PasteSpecial();
    } else {
      console.log(`未找到与城市名称 '${city}' 对应的工作表`);
    }
  });
}

// 辅助函数，用于在工作表数据中查找特定 key 的行号
function findRowByKey(data, key) {
  for (let i = 0; i < data.length; i++) {
    if (data[i][1] === key) { // 假设 key 在第二列
      return i + 1; // 返回行号
    }
  }
  return null; // 如果没有找到，返回 null
}

function xiugai(需要修改的值数组) {
  // 创建一个 Map
  let data_new = new Map();
  let data_old = new Map();

  // 遍历新增签单数据的每一行
  新增签单数据.forEach((item, index) => {
    if (index === 0) return; // 跳过表头行，表头行的索引为0

    // 获取每行数据的唯一标识（第2列的值）
    let key = item[1];
    if (key) {
      // 创建数据对象，包含：
      // - rowNumber：实际的Excel行号（index + 1，因为Excel行号从1开始）
      // - data：当前行的所有数据
      let dataWithRowNumber = { rowNumber: index + 1, data: item };
      // 将数据存入Map中，以唯一标识为键
      data_new.set(key, dataWithRowNumber);
    }
  });

  const 修改状态列索引 = 39; // 第36列的索引为35

  修改签单数据.forEach((item, index) => {
    if (index === 0) return; // 跳过标题行

    let key = item[1];
    if (key) {
      // 检查是否已经被标记为“已修改”
      if (item[修改状态列索引] !== "已修改") {
        // 将行号和数据存储在 data_old 中
        let dataWithRowNumber = { rowNumber: index + 1, data: item };
        data_old.set(key, dataWithRowNumber);
      }
    }
  });

  // 检测数据长度
  console.log(`新增签单数据长度: ${新增签单数据.length}`);
  console.log(`修改签单数据长度: ${修改签单数据.length}`);

  const 列映射数组 = [
    // 修改签单第18列，新增签单第8列（数组索引比Excel列号小1）
    { oldColumn: 17, newColumn: 7 },
    // 废单时间列第19列，新增签单第5列
    { oldColumn: 18, newColumn: 4 },
    // 成交价
    { oldColumn: 19, newColumn: 6 },
    // 订单号   
    { oldColumn: 20, newColumn: 9 }, 
    // 客户姓名  
    { oldColumn: 21, newColumn: 10 },   
    // 订单来源
    { oldColumn: 22, newColumn: 11 },   
    // 电话
    { oldColumn: 23, newColumn: 12 },   
    // 车型名称
    { oldColumn: 24, newColumn: 13 },   
    // 配置
    { oldColumn: 25, newColumn: 14 },   
    // 外观颜色
    { oldColumn: 26, newColumn: 15 },   
    // 物料代码
    { oldColumn: 27, newColumn: 16 },   
    // 指导价
    { oldColumn: 28, newColumn: 17 },   
    // 按揭
    { oldColumn: 29, newColumn: 18 },   
    // 预计交车
    { oldColumn: 30, newColumn: 20 },   
    // 销售顾问
    { oldColumn: 31, newColumn: 22 },   
    // 身份证住址
    { oldColumn: 33, newColumn: 25 },   
    // 身份证号码
    { oldColumn: 34, newColumn: 26 },   
    // 社会统一信用代码
    { oldColumn: 35, newColumn: 26 },   
    // 备注
    { oldColumn: 36, newColumn: 27 },   
    // 无法交付原因
    { oldColumn: 37, newColumn: 29 },   
    // 预计交车时间
    { oldColumn: 38, newColumn: 28 },   
  ];

  // 构建一个用于批量更新的数组
  let 修改状态更新数组 = [];
  const 修改车型列索引 = 12; 

  // 标记已更新的行
  let updatedRows = new Set();
  // 遍历 data_old 中的每个元素
  data_old.forEach((valueOld) => {
    let matchKey = valueOld.data[4]; // 获取 data_old 每个元素的第5列的值

    if (data_new.has(matchKey)) {
      // 获取 data_new 中对应的对象的 data 属性
      let 新数据对象 = data_new.get(matchKey).data;

      // 根据列映射进行赋值操作
      console.log('当前处理的valueOld:', valueOld);
      console.log('valueOld.data的完整内容:', JSON.stringify(valueOld.data));
      
      列映射数组.forEach((mapping) => {
        let 旧列值 = valueOld.data[mapping.oldColumn];
        console.log(`正在处理列映射: 旧列(${mapping.oldColumn}) -> 新列(${mapping.newColumn})`);
        console.log(`旧列${mapping.oldColumn}的索引类型:`, typeof mapping.oldColumn);
        console.log(`valueOld.data的长度:`, valueOld.data.length);
        console.log(`获取到的旧列值:`, 旧列值);
        
        if (旧列值 !== undefined && 旧列值 !== null && 旧列值 !== '') {
          新数据对象[mapping.newColumn] = 旧列值;
          console.log(`✅ 成功映射数据: 列${mapping.newColumn} = ${旧列值}`);
        } else {
          console.log(`❌ 跳过无效数据: 旧列${mapping.oldColumn} 的值为 ${旧列值}`);
        }
      });

      if (valueOld.data[修改车型列索引] === "修改订单-车型信息") {
        新数据对象[23] = ""; // 第25列置为空
        // console.log(`Updated data for key ${matchKey}: `, 新数据对象);
      }

      // 将更新过的行号添加到 updatedRows 集合中
      updatedRows.add(data_new.get(matchKey).rowNumber);

      // 添加到修改状态更新数组
      let oldRowNumber = valueOld.rowNumber;
      修改状态更新数组.push({ rowNumber: oldRowNumber, status: "已修改" });


    }
  });
  // 遍历 data_new 中的每个元素
  data_new.forEach((value, key) => {
    let rowNumber = value.rowNumber;

    if (updatedRows.has(rowNumber)) {
      let rowData = value.data;

      // 将行数据写入工作表的对应行
      for (let col = 4; col < rowData.length; col++) {
        // 只跳过第20列的更新
        if (col === 19) continue;
        let cellValue = rowData[col];
        // 将数据转换为文本格式，并在前面加上单引号
        if (/^\d{10,20}$/.test(cellValue)) {
          // 如果是10位以上的纯数字字符串，则在前面加上单引号
          cellValue = `'${cellValue}`;
        }
        新增签单工作表.Cells(rowNumber, col + 1).Value = cellValue;
      }
      // 检查第25列（索引为24）是否为“暂时不提”
      if (rowData[20] === "暂时不提" || rowData[7] === "废单") {
        新增签单工作表.Cells(rowNumber, 24).Value = ""; // 将X列（第24列）设置为空值
      }

    }
  });


  // 一次性更新“测试版修改签单”工作表
  修改状态更新数组.forEach(update => {
    // console.log(update)
    修改签单工作表.Cells(update.rowNumber, 修改状态列索引 + 1).Value = update.status;
  });


}

function tiaozhenggeshi() {
  // 一次性获取表格范围并存储在变量中
  const tableRange = 新增签单工作表.UsedRange;
  const entireTable = tableRange.Cells; // 存储整个表格的单元格

  // 设置行高
  tableRange.RowHeight = 20;

  // 设置水平对齐方式
  const xlHAlignCenter = Application.Enum.XlHAlign.xlHAlignCenter;
  entireTable.HorizontalAlignment = xlHAlignCenter;

  // 批量设置字体格式
  const startColumn = tableRange.Column;
  const totalColumns = tableRange.Columns.Count;
  const font = { Name: "华文中宋", Size: 11 };
  for (let i = startColumn; i < startColumn + totalColumns; i++) {
    const columnsRange = tableRange.Columns(i);
    columnsRange.Font.Name = font.Name;
    columnsRange.Font.Size = font.Size;
  }

  // 设置边框颜色
  const outsideBorder = tableRange.Borders.Item(Application.Enum.XlBordersIndex.xlOutside);
  const insideBorder = tableRange.Borders.Item(Application.Enum.XlBordersIndex.xlInside);
  const borderColor = '#A1A1A1';
  outsideBorder.Color = borderColor;
  insideBorder.Color = borderColor;
}

let startTime = Date.now();

let sheets = Application.ActiveWorkbook.Sheets;
let allSheetsData = 建立所有数据(sheets);

const 修改签单工作表 = Application.Sheets.Item("修改签单-优化版");
const 新增签单工作表 = Application.Sheets.Item("新增签单");
const 新增签单数据 = 新增签单工作表.UsedRange.Value
const 修改签单数据 = 修改签单工作表.UsedRange.Value

// 使用函数
tiaozhenggeshi();

// 处理“修改签单-优化版”工作表的数据
let 需要修改的值数组 = 处理修改签单数据(修改签单工作表);
console.log("需要修改的值: ", 需要修改的值数组);


let 总结的数据 = updateSheetsWithData(allSheetsData); // 保存返回值
processAndCopyData(总结的数据); // 使用这个返回值

let workbook = Application.ActiveWorkbook;
let 签单表 = workbook.Sheets.Item(2);
let 签单表数据 = new Map(); // 存储签单表的数据
let 数据不一致 = [];

// 读取签单表的数据
let 签单表格数据 = 签单表.UsedRange.Value.slice(1);
签单表格数据.forEach((行, 索引) => {
    签单表数据.set(行[1], { 
        X: 行[23], 
        Y: 行[24], 
        AC: 行[28], 
        AD: 行[29], 
        H: 行[7],  // 添加H列，索引为7
        行号: 索引 + 2 
    });
});

// 初始化其他表格数据的存储结构
let 其他表格数据Map = new Map();

// 处理第2到第13个表格，并汇总数据到Map
for (let i = 3; i <= 14; i++) {
    let 当前表 = workbook.Sheets.Item(i);
    let 当前表数据 = 当前表.UsedRange.Value.slice(1);
    当前表数据.forEach((行, 索引) => {
        let 键 = 行[1];
        let 数据 = { 
            X: 行[23], 
            Y: 行[24], 
            AC: 行[28], 
            AD: 行[29], 
            H: 行[7],  // 添加 H 列数据
            表名: 当前表.Name, 
            行号: 索引 + 2 
        };
        其他表格数据Map.set(键, 数据);
    });
}

// 对于签单表中的每一行数据，与其他表格数据进行比较
// 签单表数据.forEach((签单行数据, 键) => {
//     if (其他表格数据Map.has(键)) {
//         let 其他表数据 = 其他表格数据Map.get(键);
//         // 检查X或Y列数据不一致
//         if (签单行数据.X !== 其他表数据.X || 签单行数据.Y !== 其他表数据.Y) {
//             let 目标表 = workbook.Sheets.Item(其他表数据.表名);
//             目标表.Range(`X${其他表数据.行号}`).Value = 签单行数据.X;
//             目标表.Range(`Y${其他表数据.行号}`).Value = 签单行数据.Y;
//             数据不一致.push({ B: 键, 列: 'Y', 操作: '更新其他表' });
//         }
//         // 检查AC、AD或H列数据不一致
//         if (签单行数据.AC !== 其他表数据.AC || 签单行数据.AD !== 其他表数据.AD || 签单行数据.H !== 其他表数据.H) {
//             签单表.Range(`AC${签单行数据.行号}`).Value = 其他表数据.AC;
//             签单表.Range(`AD${签单行数据.行号}`).Value = 转换为短日期(其他表数据.AD);
//             签单表.Range(`H${签单行数据.行号}`).Value = 其他表数据.H;  // 更新H列
//             数据不一致.push({ B: 键, 列: 'AD,H', 操作: '更新签单表' });  // 修改列信息
//         }
//     }
// });

function 转换为短日期(序列号) {
    if (!序列号) return ""; // 如果序列号为空，返回空字符串
    // Excel的日期序列号基准是1900年1月1日
    let 日期 = new Date((序列号 - 1) * 24 * 60 * 60 * 1000 + new Date(1900, 0, 1).getTime());
    let 年 = 日期.getFullYear();
    let 月 = 日期.getMonth() + 1; // getMonth() 返回 0-11
    let 日 = 日期.getDate();
    return `${年}/${月}/${日}`;
}

// 输出所有不一致的数据
if (数据不一致.length > 0) {
    console.log(`发现不一致的数据：${数据不一致.length}项`);
    数据不一致.forEach(项 => {
        console.log(`B列值: ${项.B}, 不一致列: ${项.列}, 操作: ${项.操作}`);
    });
} else {
    console.log("所有数据项均一致，无需更新。");
}

let endTime = Date.now();
let elapsedTime = endTime - startTime;
console.log(`函数执行时间：${elapsedTime}毫秒`);
