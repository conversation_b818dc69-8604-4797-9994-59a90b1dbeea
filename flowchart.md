flowchart TD
    A([开始]) --> B[读取Excel工作表]
    B --> C[获取修改签单数据]
    C --> D[获取新增签单数据]
    
    D --> E{是否需要修改?}
    E -->|是| F[处理数据修改]
    E -->|否| G[保持原数据]
    
    F --> H[更新数据]
    G --> H
    
    H --> I[数据同步检查]
    I --> J{数据是否一致?}
    J -->|否| K[更新不一致数据]
    J -->|是| L[完成]
    K --> L
    
    subgraph 数据修改处理
    M[创建数据Map] --> N[更新值]
    N --> O[更新状态]
    end
    
    subgraph 数据同步
    P[读取签单表] --> Q[读取其他表]
    Q --> R[比对数据]
    R --> S[更新差异]
    end
    
    style A fill:#f96,stroke:#333
    style E fill:#f9f,stroke:#333
    style J fill:#f9f,stroke:#333
    style L fill:#96f,stroke:#333