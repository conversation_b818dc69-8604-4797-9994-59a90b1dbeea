function geshi(sheet, rowHeight) {
    // 获取有效范围
    const range = sheet.UsedRange;
    
    // 设置行高
    range.RowHeight = rowHeight;

    // 设置水平对齐方式
    range.HorizontalAlignment = -4108; // xlCenter = -4108
  
    // 设置字体
    range.Font.Name = "华文中宋";
    range.Font.Size = 11;
  
    // 设置所有单元格的边框
    const startRow = range.Row;
    const startCol = range.Column;
    const lastRow = startRow + range.Rows.Count - 1;
    const lastCol = startCol + range.Columns.Count - 1;
    
    // 使用Range获取整个区域
    const entireRange = sheet.Range(sheet.Cells(startRow, startCol), sheet.Cells(lastRow, lastCol));
    entireRange.Borders.Weight = 2;  // xlThin = 2
    // 设置灰色边框 RGB(161, 161, 161)
    entireRange.Borders.Color = 0xA1A1A1;
  
    // 设置日期格式
    sheet.Columns("A").NumberFormat = "yyyy/mm/dd";
    sheet.Columns("Y").NumberFormat = "yyyy/mm/dd";
    sheet.Columns("AE").NumberFormat = "yyyy/mm/dd";
    
    // 强制处理：第一列的日期序列号问题
    try {
        // 从第2行开始检查（假设第1行为标题行）
        for (let row = 2; row <= lastRow; row++) {
            // 获取A列单元格值
            let cellValue = sheet.Cells(row, 1).Value;
            
            // 检查是否为可能的日期序列号
            if (cellValue && typeof cellValue === 'number' && cellValue > 40000) {
                console.log(`检测到日期序列号: 行 ${row}, 值 ${cellValue}`);
                
                // 将序列号转换为标准日期字符串
                let dateStr = "";
                try {
                    // 精确计算日期
                    let serialDate = parseInt(cellValue);
                    let timeValue = cellValue - serialDate;
                    
                    // 转换成JavaScript日期
                    // Excel开始日期是1900-01-01，且有个1900年2月29日的bug，因此天数-1
                    // 但如果日期大于1900年3月1日（序列号60），则天数-2
                    let dayAdjust = serialDate > 60 ? 2 : 1;
                    let jsDate = new Date(Math.round((serialDate - dayAdjust) * 86400000) + new Date(1900, 0, 1).getTime());
                    
                    let year = jsDate.getFullYear();
                    let month = jsDate.getMonth() + 1; // 月份从0开始
                    let day = jsDate.getDate();
                    
                    // 格式化为yyyy/mm/dd
                    dateStr = `${year}/${month}/${day}`;
                    console.log(`  转换为: ${dateStr}`);
                    
                    // 将单元格设置为该文本值
                    sheet.Cells(row, 1).Value = dateStr;
                    
                    // 确保它是文本格式
                    sheet.Cells(row, 1).NumberFormat = "@";
                    
                    // 再次确认值被正确设置
                    let newValue = sheet.Cells(row, 1).Value;
                    console.log(`  设置后的值: ${newValue}`);
                } catch (e) {
                    console.log(`  日期转换出错: ${e.message}`);
                }
            }
        }
    } catch (error) {
        console.log(`处理日期序列号时出错: ${error.message}`);
    }
}
  
// 确保在处理所有工作表之前输出清晰的日志
console.log("开始处理工作表格式...");

for (let i = 3; i < 15; i++) {
    try {
        const sheetName = Application.Sheets.Item(i).Name;
        console.log(`处理工作表 ${i}: ${sheetName}`);
        geshi(Application.Sheets.Item(i), 30);
    } catch (error) {
        console.log(`处理工作表 ${i} 时出错: ${error.message}`);
    }
}

console.log("工作表格式处理完成");