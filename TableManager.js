class TableManager {
    constructor() {
        this.tableCache = new Map(); // 用于缓存表格数据
    }

    /**
     * 读取指定表格的数据
     * @param {string} sheetName 表格名称
     * @param {boolean} skipHeader 是否跳过表头，默认为true
     * @returns {Array} 表格数据
     */
    readTable(sheetName, skipHeader = true) {
        try {
            const sheet = Application.Sheets.Item(sheetName);
            if (!sheet) {
                throw new Error(`表格 ${sheetName} 不存在`);
            }
            
            let data = sheet.UsedRange.Value2;
            if (skipHeader) {
                data = data.slice(1);
            }
            
            this.tableCache.set(sheetName, data);
            return data;
        } catch (error) {
            console.error(`读取表格 ${sheetName} 失败:`, error.message);
            throw error;
        }
    }

    /**
     * 计算数据行的哈希值
     * @param {Array} row 数据行
     * @returns {string} 哈希值
     */
    calculateRowHash(row) {
        const rowString = row.join('|');
        let hash = 0;
        
        for (let i = 0; i < rowString.length; i++) {
            const char = rowString.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        
        return Math.abs(hash).toString(16);
    }

    /**
     * 处理表格数据并生成哈希映射
     * @param {string} sheetName 表格名称
     * @param {number} keyColumnIndex 用作键的列索引
     * @returns {Object} 哈希映射
     */
    processTableData(sheetName, keyColumnIndex) {
        console.time(`处理${sheetName}耗时`);
        
        try {
            const data = this.tableCache.get(sheetName) || this.readTable(sheetName);
            
            const hashMap = data.reduce((acc, row) => {
                if (row[keyColumnIndex] === undefined) {
                    throw new Error(`行数据中不存在索引 ${keyColumnIndex}`);
                }
                acc[row[keyColumnIndex]] = this.calculateRowHash(row);
                return acc;
            }, {});

            console.timeEnd(`处理${sheetName}耗时`);
            return hashMap;
        } catch (error) {
            console.error(`处理表格 ${sheetName} 失败:`, error.message);
            throw error;
        }
    }

    /**
     * 批量处理多个表格
     * @param {Array<{name: string, keyColumn: number}>} tables 表格配置数组
     * @returns {Object} 所有表格的处理结果
     */
    batchProcessTables(tables) {
        console.time('总处理耗时');
        
        try {
            const results = {};
            
            for (const table of tables) {
                results[table.name] = this.processTableData(table.name, table.keyColumn);
            }
            
            console.timeEnd('总处理耗时');
            return results;
        } catch (error) {
            console.error('批量处理表格失败:', error.message);
            throw error;
        }
    }

    /**
     * 清除缓存的表格数据
     * @param {string} [sheetName] 指定要清除的表格名称，不传则清除所有缓存
     */
    clearCache(sheetName) {
        if (sheetName) {
            this.tableCache.delete(sheetName);
        } else {
            this.tableCache.clear();
        }
    }
}

// 使用示例
const manager = new TableManager();

// 配置要处理的表格
const tables = [
    { name: "新增签单", keyColumn: 1 },
    // 可以添加更多表格配置
    // { name: "其他表格", keyColumn: 2 },
];

try {
    // 批量处理所有表格
    const results = manager.batchProcessTables(tables);
    console.log('处理结果:', results);
} catch (error) {
    console.error('程序执行失败:', error.message);
}

// 执行完成后清除缓存
manager.clearCache();