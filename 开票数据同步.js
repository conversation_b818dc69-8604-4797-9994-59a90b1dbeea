function geshi(sheetName) {
    // 获取表行
    let lastRow = sheetName.Cells.Find("*", sheetName.Cells(sheetName.Rows.Count, "A"), -4163, 1, 1, 2).Row
  
    sheetName.Range("AE2:AE" + lastRow).Formula = 
      '=IF(LEN(X2)=17,XLOOKUP(X2,\'[提车跟进表.xlsx]汇总（含欧尚库存）\'!$C:$C,\'[提车跟进表.xlsx]汇总（含欧尚库存）\'!$A:$A,"",0),"")'
    sheetName.Range("AF2:AF" + lastRow).Formula = 
      '=IF(LEN(X2)=17,XLOOKUP(X2,\'[提车跟进表.xlsx]汇总（含欧尚库存）\'!$C:$C,\'[提车跟进表.xlsx]汇总（含欧尚库存）\'!$G:$G,"",0),"")'
    sheetName.Range("AG2:AG" + lastRow).Formula = 
      '=SWITCH(TRUE(),AF2="","",AF2=K2,"一致",AF2<>K2,"不一致")'
  
  }
  
  for (i = 2;i<16;i++) {
    geshi(Application.Sheets.Item(i))
  }