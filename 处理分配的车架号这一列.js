function 处理单引号() {
    let sheet = Application.Worksheets.Item("新增签单");
    let lastRow = sheet.UsedRange.Rows.Count;
    
    if (lastRow < 2) return; // 如果只有表头或空表，直接返回
    
    // 一次性读取整列数据
    let range = sheet.Range(`X2:X${lastRow}`);
    let values = range.Value2;
    
    // 如果只有一行数据，确保 values 是二维数组
    if (!Array.isArray(values)) {
        values = [[values]];
    }
    
    // 创建新的数组来存储处理后的值
    let newValues = values.map(row => {
        let value = row[0];
        // 只有当值恰好等于单引号时才设置为空字符串，其他情况保持原值
        return [value === "'" ? "" : value];
    });
    
    // 一次性写回所有数据
    range.Value2 = newValues;
}

// 执行单引号处理
处理单引号();