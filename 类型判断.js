function 处理类型判断() {
    let sheet = Application.Worksheets.Item("主表");
    let lastRow = sheet.UsedRange.Rows.Count;
    
    if (lastRow < 2) return; // 如果只有表头或空表，直接返回
    
    // 一次性读取B列和F列数据
    let rangeB = sheet.Range(`B2:B${lastRow}`);
    let rangeF = sheet.Range(`F2:F${lastRow}`);
    let valuesB = rangeB.Value2;
    let valuesF = rangeF.Value2;
    
    // 如果只有一行数据，确保是二维数组
    if (!Array.isArray(valuesB)) {
        valuesB = [[valuesB]];
        valuesF = [[valuesF]];
    }
    
    // 创建数组存储处理后的数据（包含B、F、V三列的值）
    let processedData = valuesB.map((row, index) => {
        let valueB = String(row[0] || "");
        let valueF = String(valuesF[index][0] || "");
        let valueV;

        // 根据B列的值决定V列的值
        if (valueB.includes("启源")) {
            valueV = "启源";
        } else if (!valueB || valueB === "0") {
            valueV = "";
        } else {
            valueV = "引力";
        }

        return {
            B: valueB,
            F: valueF,
            V: valueV
        };
    });

    // 根据F列匹配规则直接回写数据到表格
    for (let i = 0; i < valuesF.length; i++) {
        let currentF = String(valuesF[i][0]);
        // 在处理后的数据中查找匹配项
        let matchedData = processedData.find(data => data.F === currentF);
        if (matchedData) {
            // 找到匹配项，直接写入对应行的V列
            sheet.Range(`V${i + 2}`).Value2 = matchedData.V;
        }
    }
}

// 执行类型判断
处理类型判断();
