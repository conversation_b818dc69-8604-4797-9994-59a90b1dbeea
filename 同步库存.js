// 性能计时函数
function 记录用时(开始时间, 步骤名称) {
    const 用时 = Date.now() - 开始时间;
    console.log(`${步骤名称}: ${用时}ms`);
    return Date.now();
}

let 开始时间 = Date.now();

// 打开指定文档
let 库存链接 = KSDrive.openFile('https://www.kdocs.cn/l/coWf4nTWsst0')
let 库存表 = 库存链接.Application.Sheets.Item('主表')
let 新增签单工作表 = Application.Worksheets.Item("新增签单");
开始时间 = 记录用时(开始时间, "打开文档");

// 获取行数
let 库存表行数 = 库存表.UsedRange.Rows.Count;
let 订单表行数 = 新增签单工作表.UsedRange.Rows.Count;

if (库存表行数 < 2 || 订单表行数 < 2) return; // 如果只有表头或空表，直接返回

// 一次性读取库存表所需数据
let 库存数据 = 库存表.UsedRange.Value2;
开始时间 = 记录用时(开始时间, "读取库存表数据");

// 一次性读取订单表所需数据
let 订单数据 = 新增签单工作表.UsedRange.Value2;
开始时间 = 记录用时(开始时间, "读取订单表数据");

// 创建配置到行号的映射
const 配置映射 = new Map();
for (let i = 1; i < 库存数据.length; i++) {
    const row = 库存数据[i];
    if (row && row[5]) {  // 库存表第6列（索引5）
        配置映射.set(row[5], i);
    }
}
开始时间 = 记录用时(开始时间, "创建映射");

// 获取需要更新的列范围
let range15 = 库存表.Range(库存表.Cells(2, 16), 库存表.Cells(库存表行数, 16));  // 第16列(对应索引15)
let range18 = 库存表.Range(库存表.Cells(2, 19), 库存表.Cells(库存表行数, 19));  // 第19列(对应索引18)
let range19 = 库存表.Range(库存表.Cells(2, 20), 库存表.Cells(库存表行数, 20));  // 第20列(对应索引19)

// 先清空这三列
range15.Value2 = range15.Resize(库存表行数 - 1, 1).Value2.map(() => [""]);
range18.Value2 = range18.Resize(库存表行数 - 1, 1).Value2.map(() => [""]);
range19.Value2 = range19.Resize(库存表行数 - 1, 1).Value2.map(() => [""]);

// 更新匹配的数据
for (let i = 1; i < 订单数据.length; i++) {
    const 订单行 = 订单数据[i];
    if (订单行 && 订单行[23]) {  // 订单表第24列（索引23）
        const 匹配行号 = 配置映射.get(订单行[23]);
        if (匹配行号 !== undefined) {
            // 更新匹配行的对应列
            库存表.Cells(匹配行号 + 1, 16).Value2 = 订单行[24];  // 第16列(对应索引15)
            库存表.Cells(匹配行号 + 1, 19).Value2 = 订单行[21];  // 第19列(对应索引18)
            库存表.Cells(匹配行号 + 1, 20).Value2 = 订单行[22];  // 第20列(对应索引19)
        }
    }
}
开始时间 = 记录用时(开始时间, "处理数据");
开始时间 = 记录用时(开始时间, "写回数据");

// 关闭文件
库存链接.close();
记录用时(开始时间, "关闭文件");

// 输出总用时
console.log(`总用时: ${Date.now() - 开始时间}ms`);
