function 公式(sheetName) {
    // 获取表行
    let lastRow = sheetName.Cells.Find("*", sheetName.Cells(sheetName.Rows.Count, "A"), -4163, 1, 1, 2).Row
    sheetName.Range("CK2:CK" + lastRow).Formula =
        '=LEFT(BJ2,1)&X1&LEFT(BN2,3)&RIGHT(BN2,4)'
    sheetName.Range("CL2:CL" + lastRow).Formula =
        '=LEFT(BJ2,1)&X1&LEFT(BN2,3)&RIGHT(BN2,4)'


}

let 链接 = KSDrive.openFile('https://www.kdocs.cn/l/csK9J94OyoTD')
公式(链接.Application.Sheets.Item('云图数据'))