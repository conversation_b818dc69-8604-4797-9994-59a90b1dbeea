function geshi(sheetName, rowHeight) {
  // 一次性获取表格范围并存储在变量中
  const tableRange = sheetName.UsedRange;
  const entireTable = tableRange.Cells; // 存储整个表格的单元格

  // 设置行高
  tableRange.RowHeight = rowHeight;

  // 设置水平对齐方式
  const xlHAlignCenter = Application.Enum.XlHAlign.xlHAlignCenter;
  entireTable.HorizontalAlignment = xlHAlignCenter;

  // 批量设置字体格式
  const startColumn = tableRange.Column;
  const totalColumns = tableRange.Columns.Count;
  const font = { Name: "华文中宋", Size: 11 };
  for (let i = startColumn; i < startColumn + totalColumns; i++) {
    const columnsRange = tableRange.Columns(i);
    columnsRange.Font.Name = font.Name;
    columnsRange.Font.Size = font.Size;
  }

  // 设置边框颜色
  const outsideBorder = tableRange.Borders.Item(Application.Enum.XlBordersIndex.xlOutside);
  const insideBorder = tableRange.Borders.Item(Application.Enum.XlBordersIndex.xlInside);
  const borderColor = '#A1A1A1';
  outsideBorder.Color = borderColor;
  insideBorder.Color = borderColor;

  // 设置Y列为日期格式
  sheetName.Columns("A").NumberFormat = "yyyy/mm/dd";
  sheetName.Columns("Y").NumberFormat = "yyyy/mm/dd";
  sheetName.Columns("AE").NumberFormat = "yyyy/mm/dd";
}

for (i = 3;i<15;i++) {
  geshi(Application.Sheets.Item(i),30)
}