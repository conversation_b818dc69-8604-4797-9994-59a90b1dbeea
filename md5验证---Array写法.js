/**
 * 计算单行数据的哈希值
 * @param {Array} row 单行数据数组
 * @returns {string} 返回哈希值
 */
const calculateRowHash = (row) => {
    // 将行数据转换为字符串
    const rowString = row.join('|');
    
    let hash = 0;
    for (let i = 0; i < rowString.length; i++) {
        const char = rowString.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32-bit integer
    }
    
    // 转换为正数并返回16进制字符串
    return Math.abs(hash).toString(16);
};

// 记录开始时间
const startTime = Date.now();

// 创建总结果对象，用于存储所有表格的数据
const allTablesResult = {};

// 循环处理Item(2)到Item(15)的表格
for (let i = 2; i <= 15; i++) {
    try {
        // 获取工作表
        const currentSheet = Application.Sheets.Item(i);
        const sheetName = currentSheet.Name;
        
        // 获取表格数据，使用Value2直接获取值数组
        const tableData = currentSheet.UsedRange.Value2;
        
        // 创建当前表格的哈希值字典
        const hashMap = {};
        
        // 从第二行开始处理（跳过表头）
        for (let rowIndex = 1; rowIndex < tableData.length; rowIndex++) {
            const row = tableData[rowIndex];
            if (row && row[1]) { // 确保行和关键字段存在
                hashMap[row[1]] = calculateRowHash(row);
            }
        }
            
        // 将当前表格的结果存入总结果对象
        allTablesResult[sheetName] = hashMap;
        
        console.log(`成功处理工作表: ${sheetName}`);
    } catch (error) {
        console.log(`处理工作表 ${i} 时出错:`, error.message);
    }
}

// 计算并显示处理耗时
const endTime = Date.now();
const duration = (endTime - startTime) / 1000; // 转换为秒
console.log(`处理耗时: ${duration}秒`);

// 打印总结果
console.log('所有表格的处理结果:', allTablesResult);

// 返回总结果
return allTablesResult;