let 开始时间 = Date.now();

// 打开指定文档
let 订单链接 = KSDrive.openFile('https://www.kdocs.cn/l/cbimhp1zyzxN')
let 订单表 = 订单链接.Application.Sheets.Item('新增签单')
let 提车跟进表 = Application.Sheets.Item('汇总（含欧尚库存）')


// 获取行数
let 订单表行数 = 订单表.UsedRange.Rows.Count;
let 提车跟进表行数 = 提车跟进表.UsedRange.Rows.Count;

if (订单表行数 < 2 || 提车跟进表行数 < 2) return; // 如果只有表头或空表，直接返回

// 一次性读取库存表所需数据
let 订单数据 = 订单表.UsedRange.Value2;

// 一次性读取订单表所需数据
let 提车跟进表数据 = 提车跟进表.UsedRange.Value2;

// 处理订单表中第24列为空的行
const 值出现次数 = new Map();  // 用于统计第11列的值出现的次数

console.log("开始处理订单数据...");
// 遍历所有订单数据行（从索引1开始跳过表头）
for (let i = 1; i < 订单数据.length; i++) {
    const row = 订单数据[i];
    // 检查三个条件：
    if (row && !row[23] && row[10]) {
        // 获取当前值的出现次数
        const count = (值出现次数.get(row[10]) || 0) + 1;
        值出现次数.set(row[10], count);
    }
}

// 创建待处理订单行（只包含唯一值）
// 创建Map存储待处理的订单行（key为第11列的值，value为行号）
const 待处理订单行 = new Map();
// 再次遍历订单数据，筛选出符合条件的行
for (let i = 1; i < 订单数据.length; i++) {
    const row = 订单数据[i];  // row是一行数据的数组，row[n]代表第n+1列的值
    if (row && !row[23] && row[10]) {  // 检查：1.行有效 2.第24列为空 3.第11列有值
        // 检查第11列的值是否只出现过一次
        if (值出现次数.get(row[10]) === 1) {
            // 如果是唯一值，则保存到待处理Map中
            // key: 第11列的值（用于后续匹配）
            // value: 当前行号（用于更新数据）
            待处理订单行.set(row[10], i);
        }
    }
}

// 在提车跟进表中查找匹配项
// 先统计第7列值的出现次数
const 值出现次数统计 = new Map();
for (let i = 1; i < 提车跟进表数据.length; i++) {
    const row = 提车跟进表数据[i];
    if (row && row[6]) {  // 第7列有值
        值出现次数统计.set(row[6], (值出现次数统计.get(row[6]) || 0) + 1);
    }
}

// 只保存唯一值的映射
const 跟进表映射 = new Map();
for (let i = 1; i < 提车跟进表数据.length; i++) {
    const row = 提车跟进表数据[i];
    if (row && row[6] && 值出现次数统计.get(row[6]) === 1) {  // 只处理唯一值
        跟进表映射.set(row[6], row[2]);  // 使用第7列的值作为键，存储第3列的值
    }
}

// 更新订单表中的匹配项
// 遍历所有待处理的订单行
// 值: 订单表中第11列的值（用于匹配跟进表的第7列）
// 行号: 该数据在订单表中的行号（从0开始计数，0代表第一行数据）
for (const [值, 行号] of 待处理订单行) {
    // 获取当前行的完整数据
    const row = 订单数据[行号];

    // 多重条件判断：
    // 1. row 确保该行数据存在
    // 2. row[7] === "已交车" 确保第8列的状态为"已交车"
    // 3. !row[23] 确保第24列（目标列）当前为空
    if (row && row[7] === "已交车" && !row[23]) {
        // 从跟进表映射中获取匹配值
        // 注意：这个映射中只包含在第7列唯一出现的值
        const 匹配值 = 跟进表映射.get(值);
        
        // 如果找到了匹配值，则更新订单表
        if (匹配值) {
            // 更新订单表的第24列
            // 行号+1：因为Excel的行号从1开始，而数组索引从0开始
            订单表.Cells(行号 + 1, 24).Value2 = 匹配值;
        }
    }
}
