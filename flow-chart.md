graph TD
    Start([开始]) --> Init[初始化工作表和变量]
    Init --> Format[调用tiaozhenggeshi<br/>设置表格样式]

    %% 主要数据处理流程
    Format --> ProcessModify[处理修改签单数据<br/>获取需要修改的值数组]
    ProcessModify --> BuildData[建立所有数据<br/>获取所有工作表数据]   
    BuildData --> UpdateSheets[updateSheetsWithData<br/>更新工作表数据]
    UpdateSheets --> ProcessCopy[processAndCopyData<br/>处理并复制数据]

    %% 数据处理分支
    ProcessCopy --> Xiugai[xiugai函数执行<br/>处理数据修改]

    %% 数据比对和更新流程
    Xiugai --> CompareData[数据比对流程]
    CompareData --> ReadSigndan[读取签单表数据]
    ReadSigndan --> ReadOthers[读取其他表格数据]

    %% 数据比对和更新
    ReadOthers --> Compare{数据比对}
    Compare -->|不一致| UpdateData[更新数据]
    Compare -->|一致| Skip[跳过]

    %% 子流程：xiugai函数
    subgraph 修改数据处理
        Xiugai --> CreateMaps[创建新旧数据Maps]
        CreateMaps --> UpdateValues[更新数据值]
        UpdateValues --> UpdateStatus[更新修改状态]
    end

    %% 子流程：数据更新
    subgraph 数据更新流程
        UpdateData --> UpdateXY[更新X/Y列]
        UpdateXY --> UpdateACH[更新AC/AD/H列]
        UpdateACH --> ConvertDate[转换日期格式]
    end

    %% 完成流程
    Skip --> Complete
    UpdateData --> Complete
    Complete([结束])

    %% 样式
    classDef process fill:#f9f,stroke:#333,stroke-width:2px
    classDef decision fill:#fdf,stroke:#333,stroke-width:2px
    class Compare decision
    class ProcessModify,BuildData,UpdateSheets,ProcessCopy process